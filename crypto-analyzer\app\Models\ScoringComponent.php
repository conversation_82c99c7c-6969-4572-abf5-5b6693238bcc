<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScoringComponent extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'component_type',
        'score',
        'weight',
        'details',
        'reasoning',
    ];

    protected $casts = [
        'score' => 'integer',
        'weight' => 'integer',
        'details' => 'array',
    ];

    /**
     * Get the project that owns the scoring component.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the weighted score for this component.
     */
    public function getWeightedScore(): float
    {
        return $this->score * ($this->weight / 100);
    }

    /**
     * Get the score grade (A, B, C, D, F).
     */
    public function getGrade(): string
    {
        if ($this->score >= 90) return 'A';
        if ($this->score >= 80) return 'B';
        if ($this->score >= 70) return 'C';
        if ($this->score >= 60) return 'D';
        return 'F';
    }

    /**
     * Get the score color for UI display.
     */
    public function getScoreColor(): string
    {
        if ($this->score >= 80) return 'green';
        if ($this->score >= 60) return 'yellow';
        if ($this->score >= 40) return 'orange';
        return 'red';
    }

    /**
     * Get the component type display name.
     */
    public function getDisplayName(): string
    {
        $names = [
            'security' => 'Security Analysis',
            'whitepaper' => 'Whitepaper Quality',
            'tokenomics' => 'Tokenomics',
            'transparency' => 'Transparency',
        ];

        return $names[$this->component_type] ?? ucfirst($this->component_type);
    }

    /**
     * Get the component type description.
     */
    public function getDescription(): string
    {
        $descriptions = [
            'security' => 'Smart contract security analysis including potential vulnerabilities, ownership risks, and trading restrictions.',
            'whitepaper' => 'Quality and completeness of the project whitepaper including technical details, roadmap, and team information.',
            'tokenomics' => 'Token economics including supply distribution, vesting schedules, and utility mechanisms.',
            'transparency' => 'Project transparency including team disclosure, audit reports, and community engagement.',
        ];

        return $descriptions[$this->component_type] ?? 'Component analysis score.';
    }

    /**
     * Check if this is a security component.
     */
    public function isSecurity(): bool
    {
        return $this->component_type === 'security';
    }

    /**
     * Check if this is a whitepaper component.
     */
    public function isWhitepaper(): bool
    {
        return $this->component_type === 'whitepaper';
    }

    /**
     * Check if this is a tokenomics component.
     */
    public function isTokenomics(): bool
    {
        return $this->component_type === 'tokenomics';
    }

    /**
     * Check if this is a transparency component.
     */
    public function isTransparency(): bool
    {
        return $this->component_type === 'transparency';
    }
}
