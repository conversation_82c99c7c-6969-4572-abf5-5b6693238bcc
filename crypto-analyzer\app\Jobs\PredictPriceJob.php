<?php

namespace App\Jobs;

use App\Models\AnalysisJob;
use App\Models\Project;
use App\Models\ScoringComponent;
use App\Services\OpenAIService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class PredictPriceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 180; // 3 minutes
    public int $tries = 3;

    private Project $project;
    private AnalysisJob $analysisJob;

    /**
     * Create a new job instance.
     */
    public function __construct(Project $project, AnalysisJob $analysisJob)
    {
        $this->project = $project;
        $this->analysisJob = $analysisJob;
    }

    /**
     * Execute the job.
     */
    public function handle(OpenAIService $openAIService): void
    {
        try {
            Log::info('Starting price prediction', [
                'project_id' => $this->project->id,
                'job_id' => $this->analysisJob->id
            ]);

            // Mark job as started
            $this->analysisJob->markAsStarted();

            // Gather tokenomics data
            $tokenomicsData = $this->gatherTokenomicsData();

            // Analyze tokenomics using OpenAI
            $tokenomicsAnalysis = $openAIService->analyzeTokenomics($tokenomicsData);

            // Calculate price predictions
            $pricePredictions = $this->calculatePricePredictions($tokenomicsData, $tokenomicsAnalysis);

            // Combine all analysis data
            $combinedAnalysis = array_merge($tokenomicsAnalysis, [
                'price_predictions' => $pricePredictions,
                'tokenomics_data' => $tokenomicsData
            ]);

            // Update project with tokenomics analysis and price prediction
            $this->project->update([
                'tokenomics_analysis' => $combinedAnalysis,
                'predicted_price' => $pricePredictions['conservative_1_year'] ?? null,
                'market_cap' => $tokenomicsData['estimated_market_cap'] ?? null
            ]);

            // Create scoring components
            $this->createTokenomicsScoringComponent($tokenomicsAnalysis);
            $this->createTransparencyScoringComponent();

            // Mark job as completed
            $this->analysisJob->markAsCompleted($combinedAnalysis);

            // Update project status if all analyses are complete
            $this->updateProjectStatus();

            Log::info('Price prediction completed', [
                'project_id' => $this->project->id,
                'predicted_price' => $pricePredictions['conservative_1_year'] ?? 'N/A',
                'tokenomics_score' => $tokenomicsAnalysis['score'] ?? 'N/A'
            ]);

        } catch (\Exception $e) {
            Log::error('Price prediction failed', [
                'project_id' => $this->project->id,
                'job_id' => $this->analysisJob->id,
                'error' => $e->getMessage()
            ]);

            // Mark job as failed
            $this->analysisJob->markAsFailed($e->getMessage());

            // Update project status
            $this->project->update([
                'status' => 'failed',
                'error_message' => 'Price prediction failed: ' . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Gather tokenomics data from various sources.
     */
    private function gatherTokenomicsData(): array
    {
        $securityAnalysis = $this->project->security_analysis ?? [];
        $whitepaperAnalysis = $this->project->whitepaper_analysis ?? [];

        return [
            'total_supply' => $this->project->total_supply,
            'holder_count' => $securityAnalysis['holder_count'] ?? null,
            'creator_balance' => $securityAnalysis['creator_balance'] ?? null,
            'creator_percent' => $securityAnalysis['creator_percent'] ?? null,
            'is_mintable' => $securityAnalysis['is_mintable'] ?? false,
            'buy_tax' => $securityAnalysis['buy_tax'] ?? '0',
            'sell_tax' => $securityAnalysis['sell_tax'] ?? '0',
            'is_in_dex' => $securityAnalysis['is_in_dex'] ?? false,
            'lp_holder_count' => $securityAnalysis['lp_holder_count'] ?? null,
            'lp_total_supply' => $securityAnalysis['lp_total_supply'] ?? null,
            'project_type' => $this->determineProjectType($whitepaperAnalysis),
            'market_category' => $this->determineMarketCategory($whitepaperAnalysis),
            'estimated_market_cap' => $this->estimateMarketCap(),
            'competition_level' => $this->assessCompetitionLevel($whitepaperAnalysis),
            'utility_score' => $whitepaperAnalysis['innovation_level'] ?? 50,
            'team_score' => $whitepaperAnalysis['team_credibility'] ?? 50,
            'technical_score' => $whitepaperAnalysis['technical_quality'] ?? 50
        ];
    }

    /**
     * Calculate price predictions based on various scenarios.
     */
    private function calculatePricePredictions(array $tokenomicsData, array $analysis): array
    {
        $totalSupply = $tokenomicsData['total_supply'] ?? 1000000000;
        $baseMarketCap = $tokenomicsData['estimated_market_cap'] ?? 1000000;

        // Adjust market cap based on analysis scores
        $overallScore = $this->project->overall_score ?? 50;
        $tokenomicsScore = $analysis['score'] ?? 50;
        
        // Score multipliers (50 = 1x, 100 = 3x, 0 = 0.1x)
        $scoreMultiplier = 0.1 + (($overallScore / 100) * 2.9);
        $tokenomicsMultiplier = 0.5 + (($tokenomicsScore / 100) * 1.5);

        // Different scenarios
        $scenarios = [
            'conservative' => $baseMarketCap * $scoreMultiplier * 0.5,
            'moderate' => $baseMarketCap * $scoreMultiplier * $tokenomicsMultiplier,
            'optimistic' => $baseMarketCap * $scoreMultiplier * $tokenomicsMultiplier * 2,
            'bull_market' => $baseMarketCap * $scoreMultiplier * $tokenomicsMultiplier * 5
        ];

        $predictions = [];
        foreach ($scenarios as $scenario => $marketCap) {
            $price = $marketCap / $totalSupply;
            $predictions[$scenario . '_1_year'] = round($price, 8);
        }

        // Add assumptions and disclaimers
        $predictions['assumptions'] = [
            'total_supply' => $totalSupply,
            'base_market_cap' => $baseMarketCap,
            'score_multiplier' => round($scoreMultiplier, 2),
            'tokenomics_multiplier' => round($tokenomicsMultiplier, 2),
            'disclaimer' => 'These are speculative predictions based on tokenomics analysis and should not be considered financial advice.'
        ];

        return $predictions;
    }

    /**
     * Determine project type from whitepaper analysis.
     */
    private function determineProjectType(array $analysis): string
    {
        $summary = strtolower($analysis['summary'] ?? '');
        
        if (strpos($summary, 'defi') !== false || strpos($summary, 'decentralized finance') !== false) {
            return 'DeFi';
        }
        if (strpos($summary, 'nft') !== false || strpos($summary, 'non-fungible') !== false) {
            return 'NFT';
        }
        if (strpos($summary, 'gaming') !== false || strpos($summary, 'game') !== false) {
            return 'Gaming';
        }
        if (strpos($summary, 'metaverse') !== false || strpos($summary, 'virtual') !== false) {
            return 'Metaverse';
        }
        if (strpos($summary, 'infrastructure') !== false || strpos($summary, 'protocol') !== false) {
            return 'Infrastructure';
        }
        
        return 'Utility';
    }

    /**
     * Determine market category.
     */
    private function determineMarketCategory(array $analysis): string
    {
        $marketPotential = $analysis['market_potential'] ?? 50;
        
        if ($marketPotential >= 80) return 'Large Market';
        if ($marketPotential >= 60) return 'Medium Market';
        return 'Niche Market';
    }

    /**
     * Estimate market cap based on project characteristics.
     */
    private function estimateMarketCap(): float
    {
        // Base market cap estimates by category
        $baseEstimates = [
            'DeFi' => 10000000,      // $10M
            'NFT' => 5000000,        // $5M
            'Gaming' => 15000000,    // $15M
            'Metaverse' => 20000000, // $20M
            'Infrastructure' => 50000000, // $50M
            'Utility' => 5000000     // $5M
        ];

        $projectType = $this->determineProjectType($this->project->whitepaper_analysis ?? []);
        return $baseEstimates[$projectType] ?? 5000000;
    }

    /**
     * Assess competition level.
     */
    private function assessCompetitionLevel(array $analysis): string
    {
        $innovationLevel = $analysis['innovation_level'] ?? 50;
        
        if ($innovationLevel >= 80) return 'Low Competition';
        if ($innovationLevel >= 60) return 'Medium Competition';
        return 'High Competition';
    }

    /**
     * Create tokenomics scoring component.
     */
    private function createTokenomicsScoringComponent(array $analysis): void
    {
        $score = $analysis['score'] ?? 50;
        
        ScoringComponent::updateOrCreate(
            [
                'project_id' => $this->project->id,
                'component_type' => 'tokenomics'
            ],
            [
                'score' => $score,
                'weight' => 20, // 20% weight as per specification
                'details' => [
                    'sustainability_rating' => $analysis['sustainability_rating'] ?? null,
                    'distribution_analysis' => $analysis['distribution_analysis'] ?? null,
                    'utility_assessment' => $analysis['utility_assessment'] ?? null,
                    'inflation_analysis' => $analysis['inflation_analysis'] ?? null,
                    'strengths' => $analysis['strengths'] ?? [],
                    'weaknesses' => $analysis['weaknesses'] ?? []
                ],
                'reasoning' => $analysis['recommendations'] ?? 'Tokenomics analysis completed'
            ]
        );
    }

    /**
     * Create transparency scoring component.
     */
    private function createTransparencyScoringComponent(): void
    {
        $securityAnalysis = $this->project->security_analysis ?? [];
        $whitepaperAnalysis = $this->project->whitepaper_analysis ?? [];
        
        // Calculate transparency score
        $score = 50; // Base score
        
        // Positive factors
        if ($securityAnalysis['is_open_source'] ?? false) $score += 20;
        if (!empty($whitepaperAnalysis['summary'])) $score += 15;
        if (($whitepaperAnalysis['team_credibility'] ?? 0) > 70) $score += 15;
        
        // Negative factors
        if ($securityAnalysis['hidden_owner'] ?? false) $score -= 30;
        if (empty($securityAnalysis['creator_address'] ?? '')) $score -= 10;
        
        $score = max(0, min(100, $score));
        
        ScoringComponent::updateOrCreate(
            [
                'project_id' => $this->project->id,
                'component_type' => 'transparency'
            ],
            [
                'score' => $score,
                'weight' => 10, // 10% weight as per specification
                'details' => [
                    'is_open_source' => $securityAnalysis['is_open_source'] ?? false,
                    'has_whitepaper' => !empty($whitepaperAnalysis['summary']),
                    'team_disclosed' => ($whitepaperAnalysis['team_credibility'] ?? 0) > 50,
                    'hidden_owner' => $securityAnalysis['hidden_owner'] ?? false
                ],
                'reasoning' => 'Transparency score based on code verification, documentation, and team disclosure'
            ]
        );
    }

    /**
     * Update project status based on completed analyses.
     */
    private function updateProjectStatus(): void
    {
        // Check if all required analyses are complete
        $completedJobs = $this->project->analysisJobs()
            ->where('status', 'completed')
            ->count();

        $totalJobs = $this->project->analysisJobs()->count();

        if ($completedJobs === $totalJobs) {
            // All analyses complete - calculate final score
            $overallScore = $this->project->calculateOverallScore();
            
            $this->project->update([
                'overall_score' => $overallScore,
                'status' => 'completed'
            ]);

            Log::info('Project analysis completed', [
                'project_id' => $this->project->id,
                'overall_score' => $overallScore
            ]);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Price prediction job failed permanently', [
            'project_id' => $this->project->id,
            'job_id' => $this->analysisJob->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts
        ]);

        // Mark job as failed if not already done
        if (!$this->analysisJob->hasFailed()) {
            $this->analysisJob->markAsFailed($exception->getMessage());
        }

        // Update project status
        $this->project->update([
            'status' => 'failed',
            'error_message' => 'Price prediction failed after ' . $this->tries . ' attempts: ' . $exception->getMessage()
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'price-prediction',
            'tokenomics-analysis',
            'project:' . $this->project->id,
            'contract:' . $this->project->contract_address
        ];
    }
}
