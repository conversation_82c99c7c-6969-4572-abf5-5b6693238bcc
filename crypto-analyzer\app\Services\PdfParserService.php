<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class PdfParserService
{
    /**
     * Extract text content from PDF file.
     */
    public function extractTextFromPdf(string $filePath): string
    {
        try {
            // Check if file exists
            if (!Storage::exists($filePath)) {
                throw new \Exception("PDF file not found: {$filePath}");
            }

            $fullPath = Storage::path($filePath);
            
            // Try using smalot/pdfparser if available
            if (class_exists('\Smalot\PdfParser\Parser')) {
                return $this->extractWithSmalotParser($fullPath);
            }
            
            // Fallback to command line tools
            return $this->extractWithCommandLine($fullPath);
            
        } catch (\Exception $e) {
            Log::error('PDF text extraction failed', [
                'error' => $e->getMessage(),
                'file_path' => $filePath
            ]);
            
            throw $e;
        }
    }

    /**
     * Extract text from URL-based whitepaper.
     */
    public function extractTextFromUrl(string $url): string
    {
        try {
            // Download the PDF temporarily
            $tempPath = $this->downloadPdfFromUrl($url);
            
            // Extract text
            $text = $this->extractTextFromPdf($tempPath);
            
            // Clean up temporary file
            Storage::delete($tempPath);
            
            return $text;
            
        } catch (\Exception $e) {
            Log::error('PDF URL text extraction failed', [
                'error' => $e->getMessage(),
                'url' => $url
            ]);
            
            throw $e;
        }
    }

    /**
     * Extract text using Smalot PDF Parser.
     */
    private function extractWithSmalotParser(string $filePath): string
    {
        $parser = new \Smalot\PdfParser\Parser();
        $pdf = $parser->parseFile($filePath);
        
        $text = $pdf->getText();
        
        return $this->cleanExtractedText($text);
    }

    /**
     * Extract text using command line tools (fallback).
     */
    private function extractWithCommandLine(string $filePath): string
    {
        // Try pdftotext command
        $command = "pdftotext \"$filePath\" -";
        $output = shell_exec($command);
        
        if ($output !== null && !empty(trim($output))) {
            return $this->cleanExtractedText($output);
        }
        
        // Try alternative command
        $command = "pdf2txt \"$filePath\"";
        $output = shell_exec($command);
        
        if ($output !== null && !empty(trim($output))) {
            return $this->cleanExtractedText($output);
        }
        
        throw new \Exception('Unable to extract text from PDF using command line tools');
    }

    /**
     * Download PDF from URL to temporary storage.
     */
    private function downloadPdfFromUrl(string $url): string
    {
        $tempFileName = 'temp_pdf_' . uniqid() . '.pdf';
        $tempPath = 'temp/' . $tempFileName;
        
        // Create temp directory if it doesn't exist
        Storage::makeDirectory('temp');
        
        // Download the file
        $content = file_get_contents($url);
        
        if ($content === false) {
            throw new \Exception("Failed to download PDF from URL: {$url}");
        }
        
        // Validate it's a PDF
        if (substr($content, 0, 4) !== '%PDF') {
            throw new \Exception("Downloaded file is not a valid PDF");
        }
        
        Storage::put($tempPath, $content);
        
        return $tempPath;
    }

    /**
     * Clean and normalize extracted text.
     */
    private function cleanExtractedText(string $text): string
    {
        // Remove excessive whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Remove control characters except newlines and tabs
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);
        
        // Normalize line breaks
        $text = str_replace(["\r\n", "\r"], "\n", $text);
        
        // Remove excessive line breaks
        $text = preg_replace('/\n{3,}/', "\n\n", $text);
        
        // Trim whitespace
        $text = trim($text);
        
        return $text;
    }

    /**
     * Validate PDF file.
     */
    public function validatePdf(string $filePath): bool
    {
        try {
            if (!Storage::exists($filePath)) {
                return false;
            }
            
            $content = Storage::get($filePath);
            
            // Check PDF header
            if (substr($content, 0, 4) !== '%PDF') {
                return false;
            }
            
            // Check file size (limit to 50MB)
            $size = Storage::size($filePath);
            if ($size > 50 * 1024 * 1024) {
                return false;
            }
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('PDF validation failed', [
                'error' => $e->getMessage(),
                'file_path' => $filePath
            ]);
            
            return false;
        }
    }

    /**
     * Get PDF metadata.
     */
    public function getPdfMetadata(string $filePath): array
    {
        try {
            if (!Storage::exists($filePath)) {
                throw new \Exception("PDF file not found: {$filePath}");
            }
            
            $fullPath = Storage::path($filePath);
            $size = Storage::size($filePath);
            
            $metadata = [
                'file_size' => $size,
                'file_size_human' => $this->formatBytes($size),
                'file_path' => $filePath,
            ];
            
            // Try to get additional metadata using smalot parser
            if (class_exists('\Smalot\PdfParser\Parser')) {
                try {
                    $parser = new \Smalot\PdfParser\Parser();
                    $pdf = $parser->parseFile($fullPath);
                    $details = $pdf->getDetails();
                    
                    $metadata = array_merge($metadata, [
                        'title' => $details['Title'] ?? null,
                        'author' => $details['Author'] ?? null,
                        'subject' => $details['Subject'] ?? null,
                        'creator' => $details['Creator'] ?? null,
                        'producer' => $details['Producer'] ?? null,
                        'creation_date' => $details['CreationDate'] ?? null,
                        'modification_date' => $details['ModDate'] ?? null,
                        'pages' => count($pdf->getPages()),
                    ]);
                } catch (\Exception $e) {
                    // Ignore metadata extraction errors
                }
            }
            
            return $metadata;
            
        } catch (\Exception $e) {
            Log::error('PDF metadata extraction failed', [
                'error' => $e->getMessage(),
                'file_path' => $filePath
            ]);
            
            return [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Extract summary from whitepaper text (first few paragraphs).
     */
    public function extractSummary(string $text, int $maxLength = 1000): string
    {
        // Split into paragraphs
        $paragraphs = explode("\n\n", $text);
        
        $summary = '';
        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            
            // Skip very short paragraphs (likely headers or page numbers)
            if (strlen($paragraph) < 50) {
                continue;
            }
            
            // Add paragraph to summary
            if (strlen($summary . $paragraph) <= $maxLength) {
                $summary .= $paragraph . "\n\n";
            } else {
                // Add partial paragraph if it fits
                $remaining = $maxLength - strlen($summary);
                if ($remaining > 100) {
                    $summary .= substr($paragraph, 0, $remaining - 3) . '...';
                }
                break;
            }
        }
        
        return trim($summary);
    }
}
