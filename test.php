<?php
echo "PHP is working!<br>";
echo "Current directory: " . __DIR__ . "<br>";
echo "PHP version: " . PHP_VERSION . "<br>";
echo "Document root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "<br>";

// Test if Laravel can be loaded
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "Composer autoload found!<br>";
    require_once __DIR__ . '/vendor/autoload.php';
    echo "Laravel vendor loaded successfully!<br>";
} else {
    echo "Composer autoload NOT found!<br>";
}
?>
