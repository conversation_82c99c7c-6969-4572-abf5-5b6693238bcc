@extends('layouts.app')

@section('title', 'New Analysis')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Analyze Crypto Project</h1>
        <p class="mt-2 text-gray-600">Upload a smart contract address and whitepaper to get a comprehensive analysis including security scan, tokenomics evaluation, and price prediction.</p>
    </div>

    <!-- Analysis Form -->
    <div class="bg-white shadow-sm rounded-lg">
        <form action="{{ route('analysis.store') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            @csrf

            <!-- Contract Information -->
            <div class="border-b border-gray-200 pb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Information</h3>
                
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <!-- Contract Address -->
                    <div>
                        <label for="contract_address" class="block text-sm font-medium text-gray-700">
                            Smart Contract Address *
                        </label>
                        <input type="text" 
                               name="contract_address" 
                               id="contract_address"
                               value="{{ old('contract_address') }}"
                               placeholder="0x..."
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('contract_address') border-red-300 @enderror">
                        @error('contract_address')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Chain ID -->
                    <div>
                        <label for="chain_id" class="block text-sm font-medium text-gray-700">
                            Blockchain Network *
                        </label>
                        <select name="chain_id" 
                                id="chain_id"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('chain_id') border-red-300 @enderror">
                            <option value="">Select Network</option>
                            <option value="1" {{ old('chain_id') == '1' ? 'selected' : '' }}>Ethereum</option>
                            <option value="56" {{ old('chain_id') == '56' ? 'selected' : '' }}>Binance Smart Chain</option>
                            <option value="137" {{ old('chain_id') == '137' ? 'selected' : '' }}>Polygon</option>
                            <option value="43114" {{ old('chain_id') == '43114' ? 'selected' : '' }}>Avalanche</option>
                        </select>
                        @error('chain_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Project Name (Optional) -->
                <div class="mt-6">
                    <label for="project_name" class="block text-sm font-medium text-gray-700">
                        Project Name (Optional)
                    </label>
                    <input type="text" 
                           name="project_name" 
                           id="project_name"
                           value="{{ old('project_name') }}"
                           placeholder="e.g., MyToken"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <p class="mt-1 text-sm text-gray-500">If not provided, we'll try to extract it from the contract</p>
                </div>
            </div>

            <!-- Whitepaper Information -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Whitepaper</h3>
                
                <!-- Whitepaper Type Selection -->
                <div class="mb-6" x-data="{ whitepaperType: '{{ old('whitepaper_type', 'pdf') }}' }">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        Whitepaper Source *
                    </label>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="radio" 
                                   name="whitepaper_type" 
                                   value="pdf"
                                   x-model="whitepaperType"
                                   class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                            <span class="ml-2 text-sm text-gray-700">Upload PDF file</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="radio" 
                                   name="whitepaper_type" 
                                   value="url"
                                   x-model="whitepaperType"
                                   class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                            <span class="ml-2 text-sm text-gray-700">Provide URL link</span>
                        </label>
                    </div>

                    <!-- PDF Upload -->
                    <div x-show="whitepaperType === 'pdf'" class="mt-4">
                        <label for="whitepaper_file" class="block text-sm font-medium text-gray-700">
                            Upload Whitepaper PDF *
                        </label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="whitepaper_file" class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
                                        <span>Upload a file</span>
                                        <input id="whitepaper_file" name="whitepaper_file" type="file" accept=".pdf" class="sr-only">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">PDF up to 50MB</p>
                            </div>
                        </div>
                        @error('whitepaper_file')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- URL Input -->
                    <div x-show="whitepaperType === 'url'" class="mt-4">
                        <label for="whitepaper_url" class="block text-sm font-medium text-gray-700">
                            Whitepaper URL *
                        </label>
                        <input type="url" 
                               name="whitepaper_url" 
                               id="whitepaper_url"
                               value="{{ old('whitepaper_url') }}"
                               placeholder="https://example.com/whitepaper.pdf"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm @error('whitepaper_url') border-red-300 @enderror">
                        @error('whitepaper_url')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end pt-6 border-t border-gray-200">
                <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <svg class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Start Analysis
                </button>
            </div>
        </form>
    </div>

    <!-- Recent Projects -->
    @if($recentProjects->count() > 0)
    <div class="mt-12">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">Recent Analyses</h2>
        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <ul class="divide-y divide-gray-200">
                @foreach($recentProjects as $project)
                <li class="px-6 py-4 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                @if($project->overall_score)
                                    <div class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium {{ getScoreBgColor($project->overall_score) }} {{ getScoreColor($project->overall_score) }}">
                                        {{ $project->overall_score }}
                                    </div>
                                @else
                                    <div class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                @endif
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ $project->name }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ $project->getChainName() }} • {{ $project->created_at->diffForHumans() }}
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {{ $project->status === 'completed' ? 'bg-green-100 text-green-800' : '' }}
                                {{ $project->status === 'analyzing' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                {{ $project->status === 'failed' ? 'bg-red-100 text-red-800' : '' }}
                                {{ $project->status === 'pending' ? 'bg-gray-100 text-gray-800' : '' }}">
                                {{ ucfirst($project->status) }}
                            </span>
                            <a href="{{ route('analysis.show', $project) }}" 
                               class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">
                                View →
                            </a>
                        </div>
                    </div>
                </li>
                @endforeach
            </ul>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script>
    // File upload handling
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.getElementById('whitepaper_file');
        const form = document.querySelector('form');
        const submitButton = form.querySelector('button[type="submit"]');
        const spinner = submitButton.querySelector('svg');

        // Handle file selection
        if (fileInput) {
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // Validate file size (50MB)
                    if (file.size > 50 * 1024 * 1024) {
                        alert('File size must be less than 50MB');
                        e.target.value = '';
                        return;
                    }
                    
                    // Validate file type
                    if (file.type !== 'application/pdf') {
                        alert('Please select a PDF file');
                        e.target.value = '';
                        return;
                    }
                }
            });
        }

        // Handle form submission
        form.addEventListener('submit', function() {
            submitButton.disabled = true;
            spinner.classList.remove('hidden');
            submitButton.innerHTML = spinner.outerHTML + ' Analyzing...';
        });
    });
</script>
@endpush
@endsection
