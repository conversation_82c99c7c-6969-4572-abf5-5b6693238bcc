<?php

namespace Tests\Feature;

use App\Models\Project;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AnalysisTest extends TestCase
{
    use RefreshDatabase;

    public function test_dashboard_loads_successfully()
    {
        $response = $this->get('/');
        $response->assertStatus(200);
        $response->assertViewIs('dashboard.index');
    }

    public function test_analysis_index_loads_successfully()
    {
        $response = $this->get('/analysis');
        $response->assertStatus(200);
        $response->assertViewIs('analysis.index');
    }

    public function test_analysis_list_loads_successfully()
    {
        $response = $this->get('/analysis/list');
        $response->assertStatus(200);
        $response->assertViewIs('analysis.list');
    }

    public function test_can_create_project_with_valid_data()
    {
        Storage::fake('local');

        $file = UploadedFile::fake()->create('whitepaper.pdf', 1000, 'application/pdf');

        $response = $this->post('/analysis', [
            'contract_address' => '0x1234567890123456789012345678901234567890',
            'chain_id' => '1',
            'project_name' => 'Test Project',
            'whitepaper_type' => 'pdf',
            'whitepaper_file' => $file,
        ]);

        $response->assertRedirect();
        $this->assertDatabaseHas('projects', [
            'name' => 'Test Project',
            'contract_address' => '0x1234567890123456789012345678901234567890',
            'chain_id' => '1',
        ]);
    }

    public function test_validation_fails_with_invalid_contract_address()
    {
        $response = $this->post('/analysis', [
            'contract_address' => 'invalid_address',
            'chain_id' => '1',
            'whitepaper_type' => 'pdf',
        ]);

        $response->assertSessionHasErrors('contract_address');
    }

    public function test_validation_fails_with_invalid_chain_id()
    {
        $response = $this->post('/analysis', [
            'contract_address' => '0x1234567890123456789012345678901234567890',
            'chain_id' => '999',
            'whitepaper_type' => 'pdf',
        ]);

        $response->assertSessionHasErrors('chain_id');
    }

    public function test_can_view_project_details()
    {
        $project = Project::factory()->create([
            'name' => 'Test Project',
            'status' => 'completed',
            'overall_score' => 85
        ]);

        $response = $this->get("/analysis/{$project->id}");
        $response->assertStatus(200);
        $response->assertViewIs('analysis.show');
        $response->assertSee('Test Project');
    }

    public function test_project_status_endpoint_returns_json()
    {
        $project = Project::factory()->create([
            'status' => 'analyzing'
        ]);

        $response = $this->get("/analysis/{$project->id}/status");
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'overall_score',
            'progress',
            'jobs',
            'scoring_components'
        ]);
    }

    public function test_can_delete_project()
    {
        $project = Project::factory()->create();

        $response = $this->delete("/analysis/{$project->id}");
        $response->assertRedirect('/analysis/list');
        $this->assertDatabaseMissing('projects', ['id' => $project->id]);
    }
}
