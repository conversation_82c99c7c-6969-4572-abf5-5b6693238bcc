<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scoring_components', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('component_type'); // 'security', 'whitepaper', 'tokenomics', 'transparency'
            $table->integer('score'); // 0-100
            $table->integer('weight'); // percentage weight in final score
            $table->json('details')->nullable(); // detailed breakdown of the score
            $table->text('reasoning')->nullable(); // explanation of the score
            $table->timestamps();
            
            $table->index(['project_id', 'component_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scoring_components');
    }
};
