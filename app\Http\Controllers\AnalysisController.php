<?php

namespace App\Http\Controllers;

use App\Jobs\AnalyzeSecurityJob;
use App\Jobs\AnalyzeWhitepaperJob;
use App\Jobs\PredictPriceJob;
use App\Models\AnalysisJob;
use App\Models\Project;
use App\Services\PdfParserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AnalysisController extends Controller
{
    private PdfParserService $pdfParserService;

    public function __construct(PdfParserService $pdfParserService)
    {
        $this->pdfParserService = $pdfParserService;
    }

    /**
     * Show the upload form.
     */
    public function index()
    {
        $recentProjects = Project::latest()
            ->take(5)
            ->get();

        return view('analysis.index', compact('recentProjects'));
    }

    /**
     * Handle project creation and analysis initiation.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'contract_address' => 'required|string|size:42|regex:/^0x[a-fA-F0-9]{40}$/',
            'chain_id' => 'required|string|in:1,56,137,43114',
            'project_name' => 'nullable|string|max:255',
            'whitepaper_type' => 'required|in:pdf,url',
            'whitepaper_file' => 'required_if:whitepaper_type,pdf|file|mimes:pdf|max:51200', // 50MB max
            'whitepaper_url' => 'required_if:whitepaper_type,url|url|max:500',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Check if project already exists
            $existingProject = Project::where('contract_address', $request->contract_address)
                ->where('chain_id', $request->chain_id)
                ->first();

            if ($existingProject) {
                return redirect()
                    ->route('analysis.show', $existingProject)
                    ->with('info', 'This project has already been analyzed.');
            }

            // Create new project
            $project = $this->createProject($request);

            // Handle whitepaper upload/processing
            $this->handleWhitepaper($project, $request);

            // Create analysis jobs
            $this->createAnalysisJobs($project);

            // Update project status
            $project->update(['status' => 'analyzing']);

            return redirect()
                ->route('analysis.show', $project)
                ->with('success', 'Project analysis has been started. Results will be available shortly.');

        } catch (\Exception $e) {
            return back()
                ->withErrors(['error' => 'Failed to start analysis: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Show project analysis results.
     */
    public function show(Project $project)
    {
        $project->load(['analysisJobs', 'scoringComponents']);

        return view('analysis.show', compact('project'));
    }

    /**
     * Get project analysis status (AJAX endpoint).
     */
    public function status(Project $project)
    {
        $project->load(['analysisJobs', 'scoringComponents']);

        return response()->json([
            'status' => $project->status,
            'overall_score' => $project->overall_score,
            'progress' => $this->calculateProgress($project),
            'jobs' => $project->analysisJobs->map(function ($job) {
                return [
                    'type' => $job->job_type,
                    'status' => $job->status,
                    'duration' => $job->getFormattedDuration(),
                    'error' => $job->error_message
                ];
            }),
            'scoring_components' => $project->scoringComponents->map(function ($component) {
                return [
                    'type' => $component->component_type,
                    'score' => $component->score,
                    'weight' => $component->weight,
                    'grade' => $component->getGrade(),
                    'color' => $component->getScoreColor()
                ];
            })
        ]);
    }

    /**
     * List all projects.
     */
    public function list()
    {
        $projects = Project::with(['scoringComponents'])
            ->latest()
            ->paginate(20);

        return view('analysis.list', compact('projects'));
    }

    /**
     * Delete a project.
     */
    public function destroy(Project $project)
    {
        try {
            // Delete associated files
            if ($project->whitepaper_path && Storage::exists($project->whitepaper_path)) {
                Storage::delete($project->whitepaper_path);
            }

            // Delete project (cascade will handle related records)
            $project->delete();

            return redirect()
                ->route('analysis.list')
                ->with('success', 'Project deleted successfully.');

        } catch (\Exception $e) {
            return back()
                ->withErrors(['error' => 'Failed to delete project: ' . $e->getMessage()]);
        }
    }

    /**
     * Create a new project record.
     */
    private function createProject(Request $request): Project
    {
        return Project::create([
            'name' => $request->project_name ?: 'Unknown Project',
            'contract_address' => $request->contract_address,
            'chain_id' => $request->chain_id,
            'whitepaper_type' => $request->whitepaper_type,
            'status' => 'pending'
        ]);
    }

    /**
     * Handle whitepaper upload or URL processing.
     */
    private function handleWhitepaper(Project $project, Request $request): void
    {
        if ($request->whitepaper_type === 'pdf' && $request->hasFile('whitepaper_file')) {
            // Handle PDF upload
            $file = $request->file('whitepaper_file');
            $filename = Str::uuid() . '.pdf';
            $path = $file->storeAs('whitepapers', $filename, 'local');

            // Validate PDF
            if (!$this->pdfParserService->validatePdf($path)) {
                Storage::delete($path);
                throw new \Exception('Invalid PDF file uploaded.');
            }

            $project->update([
                'whitepaper_path' => $path
            ]);

        } elseif ($request->whitepaper_type === 'url') {
            // Handle URL
            $project->update([
                'whitepaper_url' => $request->whitepaper_url
            ]);
        }
    }

    /**
     * Create analysis jobs for the project.
     */
    private function createAnalysisJobs(Project $project): void
    {
        // Create security analysis job
        $securityJob = AnalysisJob::create([
            'project_id' => $project->id,
            'job_type' => 'security_scan',
            'status' => 'pending',
            'job_data' => [
                'contract_address' => $project->contract_address,
                'chain_id' => $project->chain_id
            ]
        ]);

        // Create whitepaper analysis job
        $whitepaperJob = AnalysisJob::create([
            'project_id' => $project->id,
            'job_type' => 'whitepaper_analysis',
            'status' => 'pending',
            'job_data' => [
                'whitepaper_type' => $project->whitepaper_type,
                'whitepaper_path' => $project->whitepaper_path,
                'whitepaper_url' => $project->whitepaper_url
            ]
        ]);

        // Create price prediction job
        $priceJob = AnalysisJob::create([
            'project_id' => $project->id,
            'job_type' => 'price_prediction',
            'status' => 'pending',
            'job_data' => []
        ]);

        // Dispatch jobs to queue
        AnalyzeSecurityJob::dispatch($project, $securityJob);
        AnalyzeWhitepaperJob::dispatch($project, $whitepaperJob);
        PredictPriceJob::dispatch($project, $priceJob);
    }

    /**
     * Calculate analysis progress percentage.
     */
    private function calculateProgress(Project $project): int
    {
        $totalJobs = $project->analysisJobs->count();
        
        if ($totalJobs === 0) {
            return 0;
        }

        $completedJobs = $project->analysisJobs->where('status', 'completed')->count();
        $processingJobs = $project->analysisJobs->where('status', 'processing')->count();

        // Completed jobs count as 100%, processing jobs as 50%
        $progress = (($completedJobs * 100) + ($processingJobs * 50)) / $totalJobs;

        return min(100, max(0, round($progress)));
    }
}
