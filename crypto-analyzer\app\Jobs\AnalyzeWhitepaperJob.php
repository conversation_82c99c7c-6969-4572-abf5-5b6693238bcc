<?php

namespace App\Jobs;

use App\Models\AnalysisJob;
use App\Models\Project;
use App\Models\ScoringComponent;
use App\Services\OpenAIService;
use App\Services\PdfParserService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class AnalyzeWhitepaperJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300; // 5 minutes
    public int $tries = 3;

    private Project $project;
    private AnalysisJob $analysisJob;

    /**
     * Create a new job instance.
     */
    public function __construct(Project $project, AnalysisJob $analysisJob)
    {
        $this->project = $project;
        $this->analysisJob = $analysisJob;
    }

    /**
     * Execute the job.
     */
    public function handle(OpenAIService $openAIService, PdfParserService $pdfParserService): void
    {
        try {
            Log::info('Starting whitepaper analysis', [
                'project_id' => $this->project->id,
                'job_id' => $this->analysisJob->id
            ]);

            // Mark job as started
            $this->analysisJob->markAsStarted();

            // Extract whitepaper content if not already done
            $whitepaperContent = $this->extractWhitepaperContent($pdfParserService);

            if (empty($whitepaperContent)) {
                throw new \Exception('No whitepaper content found to analyze');
            }

            // Analyze whitepaper using OpenAI
            $analysis = $openAIService->analyzeWhitepaper($whitepaperContent);

            // Save analysis results to project
            $this->project->update([
                'whitepaper_analysis' => $analysis,
                'whitepaper_content' => $whitepaperContent
            ]);

            // Create scoring component for whitepaper
            $this->createWhitepaperScoringComponent($analysis);

            // Mark job as completed
            $this->analysisJob->markAsCompleted($analysis);

            // Update project status if all analyses are complete
            $this->updateProjectStatus();

            Log::info('Whitepaper analysis completed', [
                'project_id' => $this->project->id,
                'score' => $analysis['score'] ?? 'N/A'
            ]);

        } catch (\Exception $e) {
            Log::error('Whitepaper analysis failed', [
                'project_id' => $this->project->id,
                'job_id' => $this->analysisJob->id,
                'error' => $e->getMessage()
            ]);

            // Mark job as failed
            $this->analysisJob->markAsFailed($e->getMessage());

            // Update project status
            $this->project->update([
                'status' => 'failed',
                'error_message' => 'Whitepaper analysis failed: ' . $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Extract whitepaper content from PDF or URL.
     */
    private function extractWhitepaperContent(PdfParserService $pdfParserService): string
    {
        // Return existing content if already extracted
        if (!empty($this->project->whitepaper_content)) {
            return $this->project->whitepaper_content;
        }

        if ($this->project->whitepaper_type === 'pdf' && $this->project->whitepaper_path) {
            // Extract from PDF file
            return $pdfParserService->extractTextFromPdf($this->project->whitepaper_path);
        }

        if ($this->project->whitepaper_type === 'url' && $this->project->whitepaper_url) {
            // Extract from URL
            return $pdfParserService->extractTextFromUrl($this->project->whitepaper_url);
        }

        throw new \Exception('No valid whitepaper source found');
    }

    /**
     * Create scoring component for whitepaper analysis.
     */
    private function createWhitepaperScoringComponent(array $analysis): void
    {
        $score = $analysis['score'] ?? 50;
        $details = [
            'technical_quality' => $analysis['technical_quality'] ?? null,
            'team_credibility' => $analysis['team_credibility'] ?? null,
            'innovation_level' => $analysis['innovation_level'] ?? null,
            'market_potential' => $analysis['market_potential'] ?? null,
            'strengths' => $analysis['strengths'] ?? [],
            'weaknesses' => $analysis['weaknesses'] ?? [],
            'risk_factors' => $analysis['risk_factors'] ?? []
        ];

        ScoringComponent::updateOrCreate(
            [
                'project_id' => $this->project->id,
                'component_type' => 'whitepaper'
            ],
            [
                'score' => $score,
                'weight' => 30, // 30% weight as per specification
                'details' => $details,
                'reasoning' => $analysis['recommendations'] ?? 'Whitepaper analysis completed'
            ]
        );
    }

    /**
     * Update project status based on completed analyses.
     */
    private function updateProjectStatus(): void
    {
        // Check if all required analyses are complete
        $completedJobs = $this->project->analysisJobs()
            ->where('status', 'completed')
            ->count();

        $totalJobs = $this->project->analysisJobs()->count();

        if ($completedJobs === $totalJobs) {
            // All analyses complete - calculate final score
            $overallScore = $this->project->calculateOverallScore();
            
            $this->project->update([
                'overall_score' => $overallScore,
                'status' => 'completed'
            ]);

            Log::info('Project analysis completed', [
                'project_id' => $this->project->id,
                'overall_score' => $overallScore
            ]);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Whitepaper analysis job failed permanently', [
            'project_id' => $this->project->id,
            'job_id' => $this->analysisJob->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts
        ]);

        // Mark job as failed if not already done
        if (!$this->analysisJob->hasFailed()) {
            $this->analysisJob->markAsFailed($exception->getMessage());
        }

        // Update project status
        $this->project->update([
            'status' => 'failed',
            'error_message' => 'Whitepaper analysis failed after ' . $this->tries . ' attempts: ' . $exception->getMessage()
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'whitepaper-analysis',
            'project:' . $this->project->id,
            'contract:' . $this->project->contract_address
        ];
    }
}
